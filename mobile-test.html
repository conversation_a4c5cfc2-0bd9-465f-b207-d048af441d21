<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Layout Test - OneAPI Logs</title>
    <style>
        /* Import the actual mobile override CSS styles */
        /* ==============================|| MOBILE OVERRIDE FRAMEWORK ||============================== */
        /* MAXIMUM SPECIFICITY CSS TO OVERRIDE ALL EXISTING STYLES */

        @media screen and (max-width: 768px) {

          /* ==============================|| CRITICAL LAYOUT RESET ||============================== */

          /* Override body padding that causes top margin - MAXIMUM SPECIFICITY */
          html body#root,
          html body,
          body {
            padding-top: 0 !important;
            margin-top: 0 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          }

          /* Reset container margins and padding */
          .ui.container {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            max-width: 100% !important;
          }

          .main-content {
            padding: 0 !important;
            margin: 0 !important;
          }

          /* ==============================|| HEADER FIXES ||============================== */

          /* Fix header positioning and overlay issues */
          .ui.header {
            position: sticky !important;
            top: 0 !important;
            z-index: 1000 !important;
            background-color: #ffffff !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            margin: 0 !important;
            padding: 12px 16px !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
          }

          .ui.header h3 {
            font-size: 18px !important;
            font-weight: 600 !important;
            margin: 0 !important;
            line-height: 1.3 !important;
            color: #212529 !important;
          }

          /* ==============================|| TABLE CARD LAYOUT ||============================== */

          /* Hide traditional table structure */
          .ui.table {
            border: none !important;
            box-shadow: none !important;
            margin: 0 !important;
            background: transparent !important;
          }

          .ui.table thead {
            display: none !important;
          }

          /* Card-based row layout */
          .ui.table tbody tr {
            display: block !important;
            background-color: #ffffff !important;
            border: none !important;
            border-radius: 12px !important;
            margin: 16px !important;
            padding: 16px !important;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
            transition: transform 0.2s ease, box-shadow 0.2s ease !important;
          }

          .ui.table tbody tr:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12) !important;
          }

          /* Cell styling with labels */
          .ui.table tbody tr td {
            display: flex !important;
            align-items: flex-start !important;
            padding: 8px 0 !important;
            border: none !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            line-height: 1.5 !important;
            min-height: auto !important;
          }

          .ui.table tbody tr td:before {
            content: attr(data-label) !important;
            font-weight: 600 !important;
            color: #6c757d !important;
            font-size: 14px !important;
            min-width: 80px !important;
            margin-right: 12px !important;
            flex-shrink: 0 !important;
          }

          .ui.table tbody tr td > * {
            flex: 1 !important;
            margin: 0 !important;
          }

          /* ==============================|| COMPLETE BORDER REMOVAL ||============================== */

          /* Remove all table borders */
          .ui.table,
          .ui.table thead,
          .ui.table tbody,
          .ui.table tfoot,
          .ui.table tr,
          .ui.table td,
          .ui.table th {
            border: none !important;
            border-top: none !important;
            border-bottom: none !important;
            border-left: none !important;
            border-right: none !important;
            border-collapse: separate !important;
          }
        }

        /* Desktop styles for comparison */
        @media screen and (min-width: 769px) {
          body {
            padding-top: 55px;
          }

          .ui.table {
            border: 1px solid #ddd;
          }

          .ui.table td {
            border-top: 1px solid #ddd;
            padding: 8px;
          }
        }

        /* Base styles */
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          background-color: #f8f9fa;
        }

        .ui.header {
          background-color: white;
          padding: 16px;
        }

        .ui.table {
          width: 100%;
          background-color: white;
        }

        .ui.table td {
          padding: 12px;
        }

        .ui.label {
          background-color: #e9ecef;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="ui container">
        <div class="main-content">
            <div class="ui header">
                <h3>使用明细（总消耗额度：点击查看）</h3>
            </div>

            <table class="ui table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>渠道</th>
                        <th>用户</th>
                        <th>令牌</th>
                        <th>类型</th>
                        <th>模型</th>
                        <th>提示</th>
                        <th>完成</th>
                        <th>配额</th>
                        <th>用时</th>
                        <th>详情</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td data-label="时间">01-15 14:30:25</td>
                        <td data-label="渠道"><span class="ui label">1</span></td>
                        <td data-label="用户"><span class="ui label">admin</span></td>
                        <td data-label="令牌"><span class="ui label">test-token</span></td>
                        <td data-label="类型"><span class="ui label">消费</span></td>
                        <td data-label="模型"><span class="ui label">gpt-4</span></td>
                        <td data-label="提示">150</td>
                        <td data-label="完成">75</td>
                        <td data-label="配额">$0.0045</td>
                        <td data-label="用时">1250 ms</td>
                        <td data-label="详情">这是一个测试请求，用于验证移动端布局是否正常工作。文本应该正确换行，不会重叠。</td>
                    </tr>
                    <tr>
                        <td data-label="时间">01-15 14:25:10</td>
                        <td data-label="渠道"><span class="ui label">2</span></td>
                        <td data-label="用户"><span class="ui label">user123</span></td>
                        <td data-label="令牌"><span class="ui label">api-key-456</span></td>
                        <td data-label="类型"><span class="ui label">消费</span></td>
                        <td data-label="模型"><span class="ui label">claude-3-sonnet</span></td>
                        <td data-label="提示">200</td>
                        <td data-label="完成">120</td>
                        <td data-label="配额">$0.0096</td>
                        <td data-label="用时">2100 ms</td>
                        <td data-label="详情">另一个测试条目，包含更长的文本内容，用于测试文本换行和卡片布局在移动设备上的表现。</td>
                    </tr>
                    <tr>
                        <td data-label="时间">01-15 14:20:45</td>
                        <td data-label="渠道"><span class="ui label">3</span></td>
                        <td data-label="用户"><span class="ui label">developer</span></td>
                        <td data-label="令牌"><span class="ui label">dev-token-789</span></td>
                        <td data-label="类型"><span class="ui label">充值</span></td>
                        <td data-label="模型">-</td>
                        <td data-label="提示">-</td>
                        <td data-label="完成">-</td>
                        <td data-label="配额">+$10.00</td>
                        <td data-label="用时">-</td>
                        <td data-label="详情">账户充值操作</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Add some interactivity to test the mobile experience
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Mobile test page loaded');
            console.log('Screen width:', window.innerWidth);
            console.log('Is mobile view:', window.innerWidth <= 768);

            // Log when window is resized
            window.addEventListener('resize', function() {
                console.log('Window resized to:', window.innerWidth);
                console.log('Is mobile view:', window.innerWidth <= 768);
            });
        });
    </script>
</body>
</html>
