<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Layout Validation - OneAPI</title>
    <style>
        /* Base styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .test-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #212529;
        }

        .test-description {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        .status-pass {
            background-color: #d4edda;
            color: #155724;
        }

        .status-fail {
            background-color: #f8d7da;
            color: #721c24;
        }

        .test-result {
            margin: 10px 0;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }

        .result-pass {
            background-color: #d4edda;
            color: #155724;
        }

        .result-fail {
            background-color: #f8d7da;
            color: #721c24;
        }

        .mobile-preview {
            border: 2px solid #ddd;
            border-radius: 20px;
            width: 375px;
            height: 667px;
            margin: 20px auto;
            overflow: hidden;
            position: relative;
            background: white;
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            overflow-y: auto;
        }

        /* Import the mobile override styles for testing */
        @media screen and (max-width: 768px) {
          /* Critical layout reset */
          html body#root,
          html body,
          body {
            padding-top: 0 !important;
            margin-top: 0 !important;
          }
          
          /* Header fixes */
          .ui.header,
          h1.ui.header,
          h2.ui.header,
          h3.ui.header {
            position: sticky !important;
            top: 0 !important;
            z-index: 9999 !important;
            background-color: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            margin: 0 !important;
            padding: 12px 16px !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            font-size: 18px !important;
            font-weight: 600 !important;
            line-height: 1.3 !important;
          }
          
          /* Table card layout */
          .ui.table thead {
            display: none !important;
          }
          
          .ui.table tbody tr {
            display: block !important;
            background-color: #ffffff !important;
            border: none !important;
            border-radius: 12px !important;
            margin: 16px !important;
            padding: 16px !important;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
            transition: transform 0.2s ease, box-shadow 0.2s ease !important;
          }
          
          .ui.table tbody tr:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12) !important;
          }
          
          .ui.table tbody tr td {
            display: flex !important;
            align-items: flex-start !important;
            padding: 8px 0 !important;
            border: none !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            line-height: 1.5 !important;
            min-height: auto !important;
          }
          
          .ui.table tbody tr td:before {
            content: attr(data-label) !important;
            font-weight: 600 !important;
            color: #6c757d !important;
            font-size: 14px !important;
            min-width: 80px !important;
            margin-right: 12px !important;
            flex-shrink: 0 !important;
          }
          
          /* Complete border removal */
          .ui.table,
          .ui.table *,
          table,
          table * {
            border: none !important;
            border-top: none !important;
            border-bottom: none !important;
            border-left: none !important;
            border-right: none !important;
            border-collapse: separate !important;
          }
        }

        /* Desktop styles for comparison */
        @media screen and (min-width: 769px) {
          body {
            padding-top: 55px;
          }
          
          .ui.table {
            border: 1px solid #ddd;
          }
          
          .ui.table td {
            border-top: 1px solid #ddd;
            padding: 8px;
          }
        }

        .ui.header {
            background-color: white;
            padding: 16px;
            margin: 0;
        }

        .ui.table {
            width: 100%;
            background-color: white;
        }

        .ui.table td {
            padding: 12px;
        }

        .ui.label {
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h1 class="test-title">Mobile Layout Validation Results</h1>
            <p class="test-description">
                This page validates the mobile layout fixes for the OneAPI Operation Log page.
                The tests check for the four main issues reported: excessive top margins, white overlay, 
                text overlapping, and persistent borders.
            </p>
            
            <div id="test-results">
                <!-- Test results will be populated by JavaScript -->
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">Mobile Preview</h2>
            <p class="test-description">
                Below is a mobile preview showing how the Operation Log should appear on mobile devices.
                Resize your browser window to less than 768px width to see the mobile layout in action.
            </p>
            
            <div class="mobile-preview">
                <div class="mobile-screen">
                    <div class="ui header">
                        <h3>使用明细（总消耗额度：点击查看）</h3>
                    </div>
                    
                    <table class="ui table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>渠道</th>
                                <th>用户</th>
                                <th>令牌</th>
                                <th>类型</th>
                                <th>模型</th>
                                <th>提示</th>
                                <th>完成</th>
                                <th>配额</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td data-label="时间">01-15 14:30:25</td>
                                <td data-label="渠道"><span class="ui label">1</span></td>
                                <td data-label="用户"><span class="ui label">admin</span></td>
                                <td data-label="令牌"><span class="ui label">test-token</span></td>
                                <td data-label="类型"><span class="ui label">消费</span></td>
                                <td data-label="模型"><span class="ui label">gpt-4</span></td>
                                <td data-label="提示">150</td>
                                <td data-label="完成">75</td>
                                <td data-label="配额">$0.0045</td>
                                <td data-label="详情">这是一个测试请求，用于验证移动端布局是否正常工作。文本应该正确换行，不会重叠。</td>
                            </tr>
                            <tr>
                                <td data-label="时间">01-15 14:25:10</td>
                                <td data-label="渠道"><span class="ui label">2</span></td>
                                <td data-label="用户"><span class="ui label">user123</span></td>
                                <td data-label="令牌"><span class="ui label">api-key-456</span></td>
                                <td data-label="类型"><span class="ui label">消费</span></td>
                                <td data-label="模型"><span class="ui label">claude-3-sonnet</span></td>
                                <td data-label="提示">200</td>
                                <td data-label="完成">120</td>
                                <td data-label="配额">$0.0096</td>
                                <td data-label="详情">另一个测试条目，包含更长的文本内容，用于测试文本换行和卡片布局在移动设备上的表现。</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mobile layout validation tests
        function runValidationTests() {
            const results = [];
            const isMobile = window.innerWidth <= 768;
            
            // Test 1: Check if body padding-top is removed on mobile
            const bodyPaddingTop = window.getComputedStyle(document.body).paddingTop;
            const test1Pass = isMobile ? bodyPaddingTop === '0px' : true;
            results.push({
                name: 'Body Padding Top Removal',
                description: 'Ensures excessive top margin is eliminated on mobile',
                pass: test1Pass,
                details: `Body padding-top: ${bodyPaddingTop} (Expected: 0px on mobile)`
            });
            
            // Test 2: Check if header has sticky positioning on mobile
            const header = document.querySelector('.ui.header');
            const headerPosition = header ? window.getComputedStyle(header).position : 'none';
            const test2Pass = isMobile ? headerPosition === 'sticky' : true;
            results.push({
                name: 'Header Sticky Positioning',
                description: 'Ensures header sticks to top and prevents white overlay',
                pass: test2Pass,
                details: `Header position: ${headerPosition} (Expected: sticky on mobile)`
            });
            
            // Test 3: Check if table headers are hidden on mobile
            const tableHead = document.querySelector('.ui.table thead');
            const headDisplay = tableHead ? window.getComputedStyle(tableHead).display : 'none';
            const test3Pass = isMobile ? headDisplay === 'none' : true;
            results.push({
                name: 'Table Header Hiding',
                description: 'Ensures table headers are hidden for card layout',
                pass: test3Pass,
                details: `Table head display: ${headDisplay} (Expected: none on mobile)`
            });
            
            // Test 4: Check if table rows have block display on mobile
            const tableRow = document.querySelector('.ui.table tbody tr');
            const rowDisplay = tableRow ? window.getComputedStyle(tableRow).display : 'none';
            const test4Pass = isMobile ? rowDisplay === 'block' : true;
            results.push({
                name: 'Table Row Card Layout',
                description: 'Ensures table rows display as cards to prevent text overlap',
                pass: test4Pass,
                details: `Table row display: ${rowDisplay} (Expected: block on mobile)`
            });
            
            // Test 5: Check if borders are removed
            const table = document.querySelector('.ui.table');
            const tableBorder = table ? window.getComputedStyle(table).border : 'none';
            const test5Pass = isMobile ? tableBorder.includes('none') || tableBorder === '0px none' : true;
            results.push({
                name: 'Border Removal',
                description: 'Ensures all table borders are completely removed',
                pass: test5Pass,
                details: `Table border: ${tableBorder} (Expected: none on mobile)`
            });
            
            return results;
        }
        
        function displayResults(results) {
            const container = document.getElementById('test-results');
            let html = '';
            
            results.forEach(result => {
                const statusClass = result.pass ? 'status-pass' : 'status-fail';
                const resultClass = result.pass ? 'result-pass' : 'result-fail';
                const statusText = result.pass ? 'PASS' : 'FAIL';
                
                html += `
                    <div class="test-result ${resultClass}">
                        <strong>${result.name}</strong>
                        <span class="status-indicator ${statusClass}">${statusText}</span>
                        <br>
                        <small>${result.description}</small>
                        <br>
                        <code>${result.details}</code>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Run tests when page loads and when window is resized
        function runTests() {
            const results = runValidationTests();
            displayResults(results);
            
            console.log('Mobile Layout Validation Results:', results);
            console.log('Screen width:', window.innerWidth);
            console.log('Is mobile view:', window.innerWidth <= 768);
        }
        
        document.addEventListener('DOMContentLoaded', runTests);
        window.addEventListener('resize', runTests);
    </script>
</body>
</html>
