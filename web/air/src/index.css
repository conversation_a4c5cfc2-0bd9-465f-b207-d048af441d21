body {
    margin: 0;
    padding-top: 55px;
    overflow-y: scroll;
    font-family: Lato, 'Helvetica Neue', Arial, Helvetica, "Microsoft YaHei", sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scrollbar-width: none;
    color: var(--semi-color-text-0) !important;
    background-color: var( --semi-color-bg-0) !important;
    height: 100%;
}

#root {
    height: 100%;
}

/* ==============================|| AIR TEMPLATE MOBILE-FIRST RESPONSIVE DESIGN ||============================== */

/* Base mobile styles (0-768px) */
@media only screen and (max-width: 768px) {
    /* TARGETED BORDER REMOVAL - Only remove borders from layout containers */

    /* Layout containers - full width */
    .semi-layout-content {
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }

    /* Remove all borders and shadows from cards and components */
    .semi-card,
    .semi-card-body {
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        margin: 0 !important;
        background: var(--semi-color-bg-0) !important;
    }

    /* Table responsive design - convert to mobile card layout */
    .semi-table {
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        background: var(--semi-color-bg-0) !important;
    }

    .semi-table-thead {
        display: none !important; /* Hide table headers on mobile */
    }

    .semi-table-tbody .semi-table-row {
        display: block !important;
        border: none !important;
        border-radius: 0 !important;
        margin-bottom: 8px !important;
        padding: 12px !important;
        background: var(--semi-color-bg-0) !important;
        box-shadow: none !important;
        width: auto !important;
    }

    .semi-table-tbody .semi-table-row .semi-table-row-cell {
        display: block !important;
        border: none !important;
        padding: 4px 0 !important;
        text-align: left !important;
        position: relative !important;
        padding-left: 40% !important;
        width: auto !important;
        border-bottom: none !important;
    }

    .semi-table-tbody .semi-table-row .semi-table-row-cell:before {
        position: absolute !important;
        left: 0 !important;
        width: 35% !important;
        font-weight: 600 !important;
        color: var(--semi-color-text-1) !important;
        font-size: 0.9em !important;
    }

    /* Add labels for each column using nth-child selectors */
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(1):before {
        content: "时间" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(2):before {
        content: "渠道" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(3):before {
        content: "用户" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(4):before {
        content: "令牌" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(5):before {
        content: "类型" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(6):before {
        content: "模型" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(7):before {
        content: "提示" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(8):before {
        content: "补全" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(9):before {
        content: "花费" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(10):before {
        content: "Latency" !important;
    }
    .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(11):before {
        content: "详情" !important;
    }

    /* Space component - responsive flex layout */
    .semi-space {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        row-gap: 3px;
        column-gap: 10px;
    }

    /* Main content area - minimal padding */
    .main-content {
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Dashboard container - minimal padding */
    .dashboard-container {
        padding: 8px !important;
        margin: 0 !important;
    }

    /* Form styling - remove borders and compact */
    .semi-form {
        padding: 8px !important;
        border: none !important;
        box-shadow: none !important;
    }

    .semi-form .semi-form-field {
        margin-bottom: 12px !important;
    }

    .semi-input-wrapper,
    .semi-select-wrapper,
    .semi-textarea-wrapper {
        border-radius: 4px !important;
    }

    /* Button groups - stack vertically */
    .semi-button-group {
        flex-direction: column !important;
    }

    .semi-button-group .semi-button {
        margin: 2px 0 !important;
        width: 100% !important;
    }

    /* Navigation - full width */
    .semi-navigation {
        border: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    /* Hide elements that take too much space on mobile */
    .hide-on-mobile {
        display: none !important;
    }

    /* Modal adjustments */
    .semi-modal {
        margin: 8px !important;
        width: calc(100% - 16px) !important;
    }

    /* Page header adjustments */
    .semi-page-header {
        padding: 8px !important;
        border: none !important;
    }
}

/* ==============================|| TABLET RESPONSIVE DESIGN ||============================== */

/* Tablet styles (769px - 1366px) */
@media only screen and (min-width: 769px) and (max-width: 1366px) {
    .semi-layout-content {
        padding: 0 16px !important;
    }

    /* Reduce card margins and borders for tablets */
    .semi-card {
        border-radius: 4px !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    }

    /* Table adjustments for tablets */
    .semi-table {
        border-radius: 4px !important;
    }

    .semi-table-tbody > .semi-table-row > .semi-table-row-cell {
        padding: 12px 8px !important;
    }

    /* Content area adjustments */
    .main-content {
        padding: 12px !important;
    }

    .dashboard-container {
        padding: 16px !important;
    }

    /* Form adjustments */
    .semi-form {
        padding: 16px !important;
    }
}

/* ==============================|| DESKTOP RESPONSIVE DESIGN ||============================== */

/* Desktop styles (1367px+) */
@media only screen and (min-width: 1367px) {
    /* Restore full styling for desktop */
    .semi-card {
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    .semi-table {
        border-radius: 8px !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    }

    .main-content {
        padding: 16px !important;
    }

    .dashboard-container {
        padding: 20px 24px 40px !important;
    }
}

.semi-table-tbody > .semi-table-row > .semi-table-row-cell {
    padding: 16px 14px;
}

.channel-table {
    .semi-table-tbody > .semi-table-row > .semi-table-row-cell {
        padding: 16px 8px;
    }
}

.semi-layout {
    height: 100%;
}

.tableShow {
    display: revert;
}

.tableHiddle {
    display: none !important;
}

body::-webkit-scrollbar {
    display: none;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.semi-navigation-vertical {
    /*display: flex;*/
    /*flex-direction: column;*/
}

.semi-navigation-item {
    margin-bottom: 0;
}

.semi-navigation-vertical {
    /*flex: 0 0 auto;*/
    /*display: flex;*/
    /*flex-direction: column;*/
    /*width: 100%;*/
    height: 100%;
    overflow: hidden;
}

.main-content {
    padding: 4px;
    height: 100%;
}

.small-icon .icon {
    font-size: 1em !important;
}

.custom-footer {
    font-size: 1.1em;
}

@media only screen and (max-width: 600px) {
    .hide-on-mobile {
        display: none !important;
    }
}


/* 隐藏浏览器默认的滚动条 */
body {
    overflow: hidden;
}

/* 自定义滚动条样式 */
body::-webkit-scrollbar {
    width: 0;  /* 隐藏滚动条的宽度 */
}

/* Dark mode enhancements for Semi-UI components */
[theme-mode="dark"] .semi-input,
[theme-mode="dark"] .semi-input-wrapper,
[theme-mode="dark"] .semi-textarea-wrapper {
    background-color: var(--semi-color-fill-0) !important;
    border-color: var(--semi-color-border) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-input:focus,
[theme-mode="dark"] .semi-input-wrapper:focus-within,
[theme-mode="dark"] .semi-textarea-wrapper:focus-within {
    border-color: var(--semi-color-primary) !important;
    background-color: var(--semi-color-fill-0) !important;
}

[theme-mode="dark"] .semi-textarea {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
    border-color: var(--semi-color-border) !important;
}

[theme-mode="dark"] .semi-textarea:focus {
    border-color: var(--semi-color-primary) !important;
    background-color: var(--semi-color-fill-0) !important;
}

[theme-mode="dark"] .semi-select,
[theme-mode="dark"] .semi-select-selection {
    background-color: var(--semi-color-fill-0) !important;
    border-color: var(--semi-color-border) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-select:focus,
[theme-mode="dark"] .semi-select-selection:focus {
    border-color: var(--semi-color-primary) !important;
}

[theme-mode="dark"] .semi-select-option-list {
    background-color: var(--semi-color-bg-2) !important;
    border-color: var(--semi-color-border) !important;
}

[theme-mode="dark"] .semi-select-option {
    background-color: var(--semi-color-bg-2) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-select-option:hover {
    background-color: var(--semi-color-fill-1) !important;
}

[theme-mode="dark"] .semi-form-field-label {
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-tabs-content {
    background-color: var(--semi-color-bg-1) !important;
}

[theme-mode="dark"] .semi-tabs-tab {
    color: var(--semi-color-text-1) !important;
}

[theme-mode="dark"] .semi-tabs-tab-active {
    color: var(--semi-color-text-0) !important;
}

/* Code blocks and pre elements for dark mode */
[theme-mode="dark"] pre {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
    border: 1px solid var(--semi-color-border) !important;
}

[theme-mode="dark"] code {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
}

/* Message and notification components */
[theme-mode="dark"] .semi-notification {
    background-color: var(--semi-color-bg-2) !important;
    border-color: var(--semi-color-border) !important;
    color: var(--semi-color-text-0) !important;
}

[theme-mode="dark"] .semi-toast {
    background-color: var(--semi-color-bg-2) !important;
    color: var(--semi-color-text-0) !important;
}

/* Ensure all generic inputs have dark styling */
[theme-mode="dark"] input,
[theme-mode="dark"] textarea,
[theme-mode="dark"] select {
    background-color: var(--semi-color-fill-0) !important;
    color: var(--semi-color-text-0) !important;
    border: 1px solid var(--semi-color-border) !important;
}

[theme-mode="dark"] input:focus,
[theme-mode="dark"] textarea:focus,
[theme-mode="dark"] select:focus {
    border-color: var(--semi-color-primary) !important;
    background-color: var(--semi-color-fill-0) !important;
    outline: none !important;
}

/* Custom scrollbar for dark mode */
[theme-mode="dark"] *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[theme-mode="dark"] *::-webkit-scrollbar-track {
    background: var(--semi-color-fill-0);
}

[theme-mode="dark"] *::-webkit-scrollbar-thumb {
    background-color: var(--semi-color-border);
    border-radius: 4px;
}

[theme-mode="dark"] *::-webkit-scrollbar-thumb:hover {
    background-color: var(--semi-color-text-2);
}

/* Dark mode enhancements for Semi-UI Tag components */
[theme-mode="dark"] .semi-tag {
    background-color: rgba(255, 255, 255, 0.08) !important;
    color: var(--semi-color-text-0) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* Color-specific tag styles for dark mode */
[theme-mode="dark"] .semi-tag-amber {
    background-color: rgba(255, 193, 7, 0.15) !important;
    color: #ffc947 !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-blue {
    background-color: rgba(33, 150, 243, 0.15) !important;
    color: #64b5f6 !important;
    border: 1px solid rgba(33, 150, 243, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-cyan {
    background-color: rgba(0, 188, 212, 0.15) !important;
    color: #4dd0e1 !important;
    border: 1px solid rgba(0, 188, 212, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-green {
    background-color: rgba(76, 175, 80, 0.15) !important;
    color: #81c784 !important;
    border: 1px solid rgba(76, 175, 80, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-grey {
    background-color: rgba(158, 158, 158, 0.15) !important;
    color: #bdbdbd !important;
    border: 1px solid rgba(158, 158, 158, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-indigo {
    background-color: rgba(63, 81, 181, 0.15) !important;
    color: #7986cb !important;
    border: 1px solid rgba(63, 81, 181, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-light-blue {
    background-color: rgba(3, 169, 244, 0.15) !important;
    color: #4fc3f7 !important;
    border: 1px solid rgba(3, 169, 244, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-lime {
    background-color: rgba(205, 220, 57, 0.15) !important;
    color: #dce775 !important;
    border: 1px solid rgba(205, 220, 57, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-orange {
    background-color: rgba(255, 152, 0, 0.15) !important;
    color: #ffb74d !important;
    border: 1px solid rgba(255, 152, 0, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-pink {
    background-color: rgba(233, 30, 99, 0.15) !important;
    color: #f06292 !important;
    border: 1px solid rgba(233, 30, 99, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-purple {
    background-color: rgba(156, 39, 176, 0.15) !important;
    color: #ba68c8 !important;
    border: 1px solid rgba(156, 39, 176, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-red {
    background-color: rgba(244, 67, 54, 0.15) !important;
    color: #ff8a80 !important;
    border: 1px solid rgba(244, 67, 54, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-teal {
    background-color: rgba(0, 150, 136, 0.15) !important;
    color: #4db6ac !important;
    border: 1px solid rgba(0, 150, 136, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-violet {
    background-color: rgba(103, 58, 183, 0.15) !important;
    color: #9575cd !important;
    border: 1px solid rgba(103, 58, 183, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-yellow {
    background-color: rgba(255, 235, 59, 0.15) !important;
    color: #fff176 !important;
    border: 1px solid rgba(255, 235, 59, 0.3) !important;
}

[theme-mode="dark"] .semi-tag-black {
    background-color: rgba(97, 97, 97, 0.15) !important;
    color: #9e9e9e !important;
    border: 1px solid rgba(97, 97, 97, 0.3) !important;
}
